from pydantic import BaseModel
from utils.clickhouse import run_query
from utils.logger import logger
from config.config import Config
import re
import openai
from typing import List, Dict, Any, Union


class QueryTask(BaseModel):
    user_id: str
    session_id: str
    step: int
    intent: str
    prompt: str
    table_schema: str = ""  # Optional, jika splitter payload mengirim
    timestamp: str = ""


class QueryResult(BaseModel):
    """Structured result containing both the SQL query and the data returned."""
    query: str
    data: List[Dict[str, Any]]

    def model_post_init(self, __context) -> None:
        """Validate the query result after initialization."""
        # Ensure query is not empty
        if not self.query or not self.query.strip():
            raise ValueError("Query cannot be empty")

        # Ensure data is a list
        if not isinstance(self.data, list):
            raise ValueError("Data must be a list of dictionaries")

        # Validate each row in data is a dictionary
        for i, row in enumerate(self.data):
            if not isinstance(row, dict):
                raise ValueError(f"Row {i} must be a dictionary, got {type(row)}")

    @property
    def row_count(self) -> int:
        """Return the number of rows in the result."""
        return len(self.data)

    @property
    def has_data(self) -> bool:
        """Return True if the result contains data."""
        return len(self.data) > 0


class QueryStepResult(BaseModel):
    """Response model with structured result field."""
    user_id: str
    session_id: str
    step: int
    intent: str
    status: str
    result: Union[QueryResult, str]  # QueryResult for success, str for errors


# Legacy model for backward compatibility
class LegacyQueryStepResult(BaseModel):
    """Legacy response model with string result field for backward compatibility."""
    user_id: str
    session_id: str
    step: int
    intent: str
    status: str
    result: str


def is_sql(query: str) -> bool:
    """
    Determine if a query string is SQL or natural language.

    This function checks if the query starts with common SQL keywords
    and follows basic SQL patterns to distinguish from natural language.
    """
    sql_keywords = [
        "SELECT",
        "INSERT",
        "UPDATE",
        "DELETE",
        "WITH",
        "SHOW",
        "DESCRIBE",
        "EXISTS",
        "CREATE",
        "DROP",
        "ALTER",
        "TRUNCATE"
    ]

    query_upper = query.strip().upper()

    # Check if it starts with SQL keywords
    starts_with_sql = any(query_upper.startswith(kw) for kw in sql_keywords)

    if not starts_with_sql:
        return False

    # Additional checks to avoid false positives with natural language
    # that might start with SQL keywords

    # If it starts with SHOW but doesn't follow SQL pattern, it's likely natural language
    if query_upper.startswith("SHOW"):
        # Valid SQL SHOW statements
        show_patterns = ["SHOW TABLES", "SHOW DATABASES", "SHOW COLUMNS", "SHOW CREATE"]
        if not any(query_upper.startswith(pattern) for pattern in show_patterns):
            # Check if it's more like "Show me all users" (natural language)
            if "ME" in query_upper or "ALL" in query_upper.split()[1:3]:
                return False

    return True


def generate_sql_from_prompt(prompt: str, schema: str = "") -> str:
    client = openai.OpenAI(api_key=Config.OPENAI_API_KEY)
    system_prompt = (
        "Anda adalah asisten AI untuk data rumah sakit. "
        "Berdasarkan instruksi berikut, buatkan SQL ClickHouse yang valid, "
        "hanya query saja, tanpa penjelasan apapun."
    )
    if schema:
        system_prompt += f"\nSchema tabel:\n{schema}"
    response = client.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt},
        ],
        temperature=0,
        max_tokens=256,
    )
    reply = response.choices[0].message.content
    # Ambil SQL code block jika ada
    match = re.search(r"```sql\s*([\s\S]*?)```", reply)
    sql = match.group(1).strip() if match else reply.strip()
    return sql


def handle_query_task(data: dict) -> dict:
    """
    Handle a query task and return structured response with query and data.
    """
    try:
        task = QueryTask(**data)
        logger.info(f"🟢 Execute for session={task.session_id} step={task.step}")
        user_prompt = task.prompt
        schema = getattr(task, "schema", "")

        # Determine if input is SQL or natural language
        if is_sql(user_prompt):
            sql = user_prompt
            logger.info(f"🟩 Detected SQL, executing: {sql}")
        else:
            logger.info(
                "🔄 Detected natural language instruction, generating SQL via LLM..."
            )
            sql = generate_sql_from_prompt(user_prompt, schema)
            logger.info(f"🔑 SQL generated: {sql}")

        # Execute query and get structured results
        sql_executed, query_data, success = run_query(sql)

        if success:
            # Create structured result
            structured_result = QueryResult(
                query=sql_executed,
                data=query_data
            )

            response = QueryStepResult(
                user_id=task.user_id,
                session_id=task.session_id,
                step=task.step,
                intent=task.intent,
                status="success",
                result=structured_result,
            )

            logger.info(f"✅ Query executed successfully, returned {len(query_data)} rows")
            return response.model_dump()
        else:
            # Handle query execution error
            error_msg = f"❌ Query execution failed: {sql_executed}"
            logger.error(error_msg)

            response = QueryStepResult(
                user_id=task.user_id,
                session_id=task.session_id,
                step=task.step,
                intent=task.intent,
                status="error",
                result=error_msg,
            )
            return response.model_dump()

    except Exception as e:
        logger.error(f"❌ Failed to handle query task: {e}")
        return {
            "user_id": data.get("user_id", ""),
            "session_id": data.get("session_id", ""),
            "step": data.get("step", 1),
            "intent": data.get("intent", "query"),
            "status": "error",
            "result": str(e),
        }


def handle_query_task_legacy(data: dict) -> dict:
    """
    Legacy handler that returns the old string-based result format for backward compatibility.
    """
    try:
        from utils.clickhouse import run_query_legacy

        task = QueryTask(**data)
        logger.info(f"🟢 Execute (legacy) for session={task.session_id} step={task.step}")
        user_prompt = task.prompt
        schema = getattr(task, "schema", "")

        if is_sql(user_prompt):
            sql = user_prompt
            logger.info(f"🟩 Detected SQL, executing: {sql}")
        else:
            logger.info(
                "🔄 Detected natural language instruction, generating SQL via LLM..."
            )
            sql = generate_sql_from_prompt(user_prompt, schema)
            logger.info(f"🔑 SQL generated: {sql}")

        result = run_query_legacy(sql)
        status = "success" if not result.startswith("❌") else "error"

        return LegacyQueryStepResult(
            user_id=task.user_id,
            session_id=task.session_id,
            step=task.step,
            intent=task.intent,
            status=status,
            result=result,
        ).model_dump()

    except Exception as e:
        logger.error(f"❌ Failed to handle legacy query task: {e}")
        return {
            "user_id": data.get("user_id", ""),
            "session_id": data.get("session_id", ""),
            "step": data.get("step", 1),
            "intent": data.get("intent", "query"),
            "status": "error",
            "result": str(e),
        }
