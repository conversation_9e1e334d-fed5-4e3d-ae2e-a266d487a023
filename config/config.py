import os
from dotenv import load_dotenv

load_dotenv()


class Config:
    SERVICE_NAME = os.getenv("SERVICE_NAME", "fin_query_worker")

    RABBITMQ_HOST = os.getenv("RABBITMQ_HOST", "localhost")
    RABBITMQ_PORT = int(os.getenv("RABBITMQ_PORT", 5672))
    RABBITMQ_USER = os.getenv("RABBITMQ_USER", "admin")
    RABBITMQ_PASSWORD = os.getenv("RABBITMQ_PASSWORD", "password")
    QUEUE_QUERY_REQUEST = os.getenv("QUEUE_QUERY_REQUEST", "query.request")
    QUEUE_QUERY_RESPONSE = os.getenv("QUEUE_QUERY_RESPONSE", "task.steps")

    CLICKHOUSE_HTTP = os.getenv("CLICKHOUSE_HTTP", "http://localhost:8123")
    CLICKHOUSE_USER = os.getenv("CLICKHOUSE_USER", "admin")
    CLICKHOUSE_PASSWORD = os.getenv("CLICKHOUSE_PASSWORD", "secret123")
    CLICKHOUSE_DB = os.getenv("CLICKHOUSE_DB", "finity")

    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

    POSTGRES_HOST = os.getenv("POSTGRES_HOST", "localhost")
    POSTGRES_PORT = int(os.getenv("POSTGRES_PORT", 5432))
    POSTGRES_USER = os.getenv("POSTGRES_USER", "postgres")
    POSTGRES_PASSWORD = os.getenv("POSTGRES_PASSWORD", "postgres")
    POSTGRES_DB = os.getenv("POSTGRES_DB", "postgres")
