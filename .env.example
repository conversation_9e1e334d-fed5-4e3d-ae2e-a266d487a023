# Service name
SERVICE_NAME=fin_query

# RabbitMQ
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=password
QUEUE_QUERY_REQUEST=query.request
QUEUE_CHAT_RESPONSE=task.steps

OPENAI_API_KEY=xxx

# ClickHouse HTTP endpoint
CLICKHOUSE_HTTP=http://localhost:8123

# PostgreSQL (opsional, jika perlu integrasi metadata/registry)
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=postgres
