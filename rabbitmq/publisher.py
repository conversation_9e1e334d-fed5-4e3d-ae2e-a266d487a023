import pika
import json
from config.config import Config
from utils.logger import logger


def publish_step_result(payload: dict):
    connection = pika.BlockingConnection(
        pika.ConnectionParameters(
            host=Config.RABBITMQ_HOST,
            port=Config.RABBITMQ_PORT,
            credentials=pika.PlainCredentials(
                Config.RABBITMQ_USER, Config.RABBITMQ_PASSWORD
            ),
        )
    )
    channel = connection.channel()
    channel.queue_declare(queue=Config.QUEUE_QUERY_RESPONSE, durable=True)
    channel.basic_publish(
        exchange="",
        routing_key=Config.QUEUE_QUERY_RESPONSE,
        body=json.dumps(payload),
        properties=pika.BasicProperties(delivery_mode=2),
    )
    logger.info(f"📤 Published step result to {Config.QUEUE_QUERY_RESPONSE}")
    connection.close()
