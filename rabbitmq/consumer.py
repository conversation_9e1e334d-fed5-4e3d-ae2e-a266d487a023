import pika
import json
from config.config import Config
from agent.query_worker import handle_query_task
from rabbitmq.publisher import publish_step_result
from utils.logger import logger


def callback(ch, method, properties, body):
    try:
        data = json.loads(body)
        logger.info(
            f"📥 Query job received: session={data.get('session_id')} step={data.get('step')}"
        )
        result = handle_query_task(data)
        publish_step_result(result)
        ch.basic_ack(delivery_tag=method.delivery_tag)
    except Exception as e:
        logger.error(f"❌ Consumer error: {e}")
        ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)


def start_consumer():
    connection = pika.BlockingConnection(
        pika.ConnectionParameters(
            host=Config.RABBITMQ_HOST,
            port=Config.RABBITMQ_PORT,
            credentials=pika.PlainCredentials(
                Config.RABBITMQ_USER, Config.RABBITMQ_PASSWORD
            ),
        )
    )
    channel = connection.channel()
    channel.queue_declare(queue=Config.QUEUE_QUERY_REQUEST, durable=True)
    channel.basic_qos(prefetch_count=1)
    channel.basic_consume(
        queue=Config.QUEUE_QUERY_REQUEST, on_message_callback=callback
    )
    logger.info(
        f"🚀 fin_query_worker is running. Waiting for tasks on {Config.QUEUE_QUERY_REQUEST} ..."
    )
    try:
        channel.start_consuming()
    except KeyboardInterrupt:
        logger.info("🛑 Interrupted, shutting down.")
        channel.stop_consuming()
    finally:
        connection.close()
