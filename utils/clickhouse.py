import requests
import json
from config.config import Config
from typing import Dict, List, Any, <PERSON><PERSON>


def run_query(sql: str) -> Tuple[str, List[Dict[str, Any]], bool]:
    """
    Execute a SQL query against ClickHouse and return structured results.

    Returns:
        Tuple containing:
        - sql_executed: The actual SQL query that was executed
        - data: List of dictionaries containing the query results
        - success: <PERSON><PERSON><PERSON> indicating if the query was successful
    """
    db = getattr(Config, "CLICKHOUSE_DB", None)
    url = f"{Config.CLICKHOUSE_HTTP}/?default_format=JSON"
    if db:
        url += f"&database={db}"

    # Clean up the SQL query
    sql_to_run = "\n".join(
        line
        for line in sql.splitlines()
        if not line.strip().lower().startswith("use ")
    )

    try:
        auth = None
        user = getattr(Config, "CLICKHOUSE_USER", None)
        password = getattr(Config, "CLICKHOUSE_PASSWORD", None)
        if user and password:
            auth = (user, password)

        response = requests.post(
            url, data=sql_to_run.encode("utf-8"), timeout=30, auth=auth
        )
        response.raise_for_status()

        # Parse JSON response
        try:
            result_data = json.loads(response.text)
            # ClickHouse returns data in 'data' field when using JSON format
            data = result_data.get('data', []) if isinstance(result_data, dict) else []
            return sql_to_run, data, True
        except json.JSONDecodeError:
            # If response is not JSON, treat as raw text (for some ClickHouse operations)
            # This handles cases like SHOW TABLES, DESCRIBE, etc.
            lines = response.text.strip().split('\n')
            if lines and lines[0]:
                # Convert plain text response to structured format
                data = [{"result": line} for line in lines if line.strip()]
            else:
                data = []
            return sql_to_run, data, True

    except requests.exceptions.RequestException as e:
        error_msg = f"❌ ClickHouse connection error: {e}\nQuery sent:\n{sql_to_run}"
        return sql_to_run, [], False
    except json.JSONDecodeError as e:
        error_msg = f"❌ Failed to parse ClickHouse response as JSON: {e}\nQuery sent:\n{sql_to_run}"
        return sql_to_run, [], False
    except Exception as e:
        error_msg = f"❌ Unexpected error querying ClickHouse: {e}\nQuery sent:\n{sql_to_run}"
        return sql_to_run, [], False


def run_query_legacy(sql: str) -> str:
    """
    Legacy function that returns raw text response for backward compatibility.
    """
    sql_executed, data, success = run_query(sql)

    if not success:
        return f"❌ Error querying ClickHouse\nQuery sent:\n{sql_executed}"

    if not data:
        return "Query executed successfully. No data returned."

    # Convert structured data back to text format for legacy compatibility
    try:
        return json.dumps({"data": data}, indent=2)
    except Exception:
        return str(data)
