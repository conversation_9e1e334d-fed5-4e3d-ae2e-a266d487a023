# fin_query - SQL Query Processing Service

A Python-based microservice that processes SQL queries and natural language instructions, executing them against ClickHouse database and returning structured results.

## Features

- **SQL Query Execution**: Direct execution of SQL queries against ClickHouse
- **Natural Language Processing**: Convert natural language instructions to SQL using OpenAI GPT
- **Structured Responses**: Returns both the executed query and results in a structured format
- **Message Queue Integration**: Uses RabbitMQ for asynchronous task processing
- **Error Handling**: Comprehensive error handling and validation
- **Backward Compatibility**: Supports both new structured and legacy response formats

## Quick Start

### Prerequisites

- Python 3.8+
- ClickHouse database
- RabbitMQ message broker
- OpenAI API key (for natural language processing)

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd fin_query
   ```

2. Create virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Configure environment:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. Start the service:
   ```bash
   python main.py
   ```

## Configuration

Configure the service using environment variables in `.env`:

```env
# Service Configuration
SERVICE_NAME=fin_query

# RabbitMQ Configuration
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=password
QUEUE_QUERY_REQUEST=query.request
QUEUE_QUERY_RESPONSE=task.steps

# ClickHouse Configuration
CLICKHOUSE_HTTP=http://localhost:8123
CLICKHOUSE_USER=admin
CLICKHOUSE_PASSWORD=secret123
CLICKHOUSE_DB=finity

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
```

## API Response Format

### New Structured Format (v2.0)

```json
{
  "user_id": "user-001",
  "session_id": "sess-005",
  "step": 1,
  "intent": "query",
  "status": "success",
  "result": {
    "query": "SELECT * FROM users WHERE id = 1",
    "data": [
      {"id": 1, "name": "John Doe", "email": "<EMAIL>"}
    ]
  }
}
```

### Legacy Format (v1.0)

```json
{
  "user_id": "user-001",
  "session_id": "sess-005",
  "step": 1,
  "intent": "query",
  "status": "success",
  "result": "{\"data\": [{\"id\": 1, \"name\": \"John Doe\"}]}"
}
```

For detailed API documentation, see [docs/API_RESPONSE_FORMAT.md](docs/API_RESPONSE_FORMAT.md).

## Usage Examples

### SQL Query

Send a message to the `query.request` queue:

```json
{
  "user_id": "user-001",
  "session_id": "sess-005",
  "step": 1,
  "intent": "query",
  "prompt": "SELECT * FROM users LIMIT 5"
}
```

### Natural Language Query

```json
{
  "user_id": "user-001",
  "session_id": "sess-005",
  "step": 1,
  "intent": "query",
  "prompt": "Show me all users created in the last 7 days"
}
```



## Deployment

### Using PM2

1. Deploy with PM2:
   ```bash
   ./scripts/pm2_deploy.sh
   ```

2. Check status:
   ```bash
   ./scripts/pm2_status.sh
   ```

3. Restart service:
   ```bash
   ./scripts/pm2_restart.sh
   ```

### Docker (Optional)

Create a Dockerfile for containerized deployment:

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "main.py"]
```

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   RabbitMQ      │    │   fin_query     │    │   ClickHouse    │
│                 │    │                 │    │                 │
│ query.request ──┼───▶│ Query Worker    │───▶│   Database      │
│                 │    │                 │    │                 │
│ task.steps   ◀──┼────│ Response        │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   OpenAI API    │
                       │                 │
                       │ Natural Language│
                       │ to SQL          │
                       └─────────────────┘
```

## Project Structure

```
fin_query/
├── agent/
│   └── query_worker.py      # Main query processing logic
├── config/
│   └── config.py           # Configuration management
├── rabbitmq/
│   ├── consumer.py         # RabbitMQ message consumer
│   └── publisher.py        # RabbitMQ message publisher
├── utils/
│   ├── clickhouse.py       # ClickHouse database utilities
│   └── logger.py           # Logging configuration
├── scripts/
│   ├── pm2_deploy.sh       # PM2 deployment script
│   ├── pm2_restart.sh      # PM2 restart script
│   └── pm2_status.sh       # PM2 status script
├── docs/
│   └── API_RESPONSE_FORMAT.md # API documentation
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
└── ecosystem.config.js     # PM2 configuration
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test your changes thoroughly
5. Submit a pull request

## License

[Add your license information here]

## Changelog

### v2.0.0
- **BREAKING**: Updated response format to include structured result with query and data fields
- Added comprehensive test suite
- Improved error handling and validation
- Added backward compatibility support
- Enhanced documentation

### v1.0.0
- Initial release with basic query processing functionality
