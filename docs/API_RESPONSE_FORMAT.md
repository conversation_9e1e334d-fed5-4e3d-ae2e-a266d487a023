# API Response Format Documentation

## Overview

This document describes the response format for the fin_query service. The service has been updated to provide structured responses that include both the executed SQL query and the resulting data.

## New Response Structure (v2.0)

### Successful Query Response

```json
{
  "user_id": "user-001",
  "session_id": "sess-005",
  "step": 1,
  "intent": "query",
  "status": "success",
  "result": {
    "query": "SELECT id, name, email FROM users WHERE id = 1",
    "data": [
      {
        "id": 1,
        "name": "<PERSON>",
        "email": "<EMAIL>"
      }
    ]
  }
}
```

### Error Response

```json
{
  "user_id": "user-001",
  "session_id": "sess-005",
  "step": 1,
  "intent": "query",
  "status": "error",
  "result": "❌ Query execution failed: Table 'nonexistent_table' doesn't exist"
}
```

### Empty Result Response

```json
{
  "user_id": "user-001",
  "session_id": "sess-005",
  "step": 1,
  "intent": "query",
  "status": "success",
  "result": {
    "query": "SELECT * FROM users WHERE id = 999",
    "data": []
  }
}
```

## Response Fields

### Root Level Fields

| Field | Type | Description |
|-------|------|-------------|
| `user_id` | string | Unique identifier for the user |
| `session_id` | string | Unique identifier for the session |
| `step` | integer | Step number in the query sequence |
| `intent` | string | Type of operation (typically "query") |
| `status` | string | Either "success" or "error" |
| `result` | object/string | Query result (object for success, string for errors) |

### Result Object Fields (Success)

| Field | Type | Description |
|-------|------|-------------|
| `query` | string | The actual SQL query that was executed |
| `data` | array | Array of objects representing the query results |

### Data Array

- Each element in the `data` array is an object representing a row from the query result
- Column names are used as object keys
- Values are preserved in their original types (string, number, boolean, null)

## Legacy Response Format (v1.0)

For backward compatibility, the legacy format is still supported:

```json
{
  "user_id": "user-001",
  "session_id": "sess-005",
  "step": 1,
  "intent": "query",
  "status": "success",
  "result": "{\"data\": [{\"id\": 1, \"name\": \"John Doe\"}]}"
}
```

## Migration Guide

### For API Consumers

1. **Check the `result` field type**:
   - If `result` is an object, you're receiving the new format
   - If `result` is a string, you're receiving the legacy format

2. **Update your parsing logic**:
   ```python
   # Old way
   if response["status"] == "success":
       data = json.loads(response["result"])["data"]
   
   # New way
   if response["status"] == "success":
       if isinstance(response["result"], dict):
           # New format
           query = response["result"]["query"]
           data = response["result"]["data"]
       else:
           # Legacy format
           data = json.loads(response["result"])["data"]
   ```

3. **Benefits of the new format**:
   - Direct access to the executed SQL query
   - No need for JSON parsing of the result field
   - Better type safety and validation
   - Clearer distinction between query and data

### For Service Maintainers

The service provides both formats through different handler functions:

- `handle_query_task()` - Returns new structured format
- `handle_query_task_legacy()` - Returns legacy string format

## Error Handling

### Common Error Types

1. **SQL Syntax Errors**:
   ```json
   {
     "status": "error",
     "result": "❌ Query execution failed: Syntax error near 'SELCT'"
   }
   ```

2. **Connection Errors**:
   ```json
   {
     "status": "error",
     "result": "❌ ClickHouse connection error: Connection refused"
   }
   ```

3. **Permission Errors**:
   ```json
   {
     "status": "error",
     "result": "❌ Query execution failed: Access denied for table 'sensitive_data'"
   }
   ```

## Examples

### Natural Language Query

**Input**: "Show me all users with email containing 'gmail'"

**Response**:
```json
{
  "user_id": "user-001",
  "session_id": "sess-005",
  "step": 1,
  "intent": "query",
  "status": "success",
  "result": {
    "query": "SELECT * FROM users WHERE email LIKE '%gmail%'",
    "data": [
      {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "created_at": "2024-01-15T10:30:00Z"
      },
      {
        "id": 3,
        "name": "Alice Smith",
        "email": "<EMAIL>",
        "created_at": "2024-01-16T14:22:00Z"
      }
    ]
  }
}
```

### Direct SQL Query

**Input**: "SELECT COUNT(*) as total_users FROM users"

**Response**:
```json
{
  "user_id": "user-001",
  "session_id": "sess-005",
  "step": 1,
  "intent": "query",
  "status": "success",
  "result": {
    "query": "SELECT COUNT(*) as total_users FROM users",
    "data": [
      {
        "total_users": 150
      }
    ]
  }
}
```

## Validation

The response format includes built-in validation to ensure data integrity:

- Query field validation (non-empty strings)
- Data type validation (arrays of objects)
- Error handling for malformed responses
- Backward compatibility with legacy format
- Proper handling of edge cases (empty results, connection errors, etc.)
